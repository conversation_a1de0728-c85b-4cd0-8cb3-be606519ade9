import { OrgId, Project, ProjectPublicId, Queue, QueueWorker, QueueWorkerCallbackOptions } from "@flakiness/database";
import { FlakinessReport } from "@flakiness/report";
import { Multimap } from "@flakiness/shared/common/multimap.js";
import assert from "assert";
import debug from "debug";
import ms from "ms";

import { bytes } from "../common/bytes.js";
import { SharedCacheStore } from "../common/caches/cache.js";
import { Ranges } from "../common/ranges.js";
import { SingleflightCache } from "../common/singleflightcache.js";
import { Stats } from "../common/stats/stats.js";
import { WireTypes } from "../common/wireTypes.js";
import { CachedDatabase } from "./cachedDatabase.js";
import { CronService } from "./cronService.js";
import { GitWorker } from "./gitWorker.js";
import { ReportIndex } from "./reportIndex.js";
import { S3GarbageCollector } from "./s3GarbageCollector.js";
import { S3Report, S3ReportIndex, S3Stats } from "./s3layout.js";
import { S3Objects } from "./s3object.js";
import { ServerTiming } from "./serverTiming.js";
import { StatsWorker } from "./statsWorker.js";
import { StripeApp } from "./stripeApp.js";
import { XNotify, XNotifyChannel } from "./xnotify.js";

const log = debug('fk:upload_worker');

const MAX_REPORTS_PER_SHARD = 10000;
const MAX_SHARD_SIZE = bytes('10 mb'); // 10MB; since it's a compressed size, the uncompressed shard would be ~40MB

type ReportIndexJob = {
  type: 'add-reports',
  projectPublicId: ProjectPublicId,
  reportIds: Ranges.Ranges<Stats.RunId>,
} | {
  type: 'enforce-data-retention',
  projectPublicId: ProjectPublicId,
} | {
  type: 'ensure-all-reports-are-in-index',
  projectPublicId: ProjectPublicId,
}

export class UploadWorker {
  private _reportIndexQueue: Queue<ReportIndexJob>;
  private _reportIndexWorker?: QueueWorker<ReportIndexJob>;

  private _xReportIndexChanged: XNotifyChannel<S3ReportIndex.Id>;
  private _cachedStorage: SingleflightCache<S3ReportIndex.Id, ReportIndex.ReportIndex>;

  // TODO: This is an unfortunate cyclic dependency bewteen uploadWorker and StripeApp.
  // Ideally, uploadWorker knows nothing about StripeApp and billing, HOWEVER, the
  // max data retention is defined by the active subscription.
  private _stripeApp?: StripeApp;

  constructor(
    memoryCache: SharedCacheStore,
    private _cron: CronService,
    private _db: CachedDatabase,
    private _s3objects: S3Objects,
    private _statsWorker: StatsWorker,
    private _gitWorker: GitWorker,
    private _s3gc: S3GarbageCollector,
    xnotify: XNotify,
    serverTiming: ServerTiming,
  ) {
    this._reportIndexQueue = this._db.queues.createQueue('report-index-queue');

    this._cachedStorage = new SingleflightCache({
      cache: memoryCache,
      ttl: ms('10min'),
      size: 100,
      key: id => S3ReportIndex.path(id),
      onGetTiming: (repoId, key, method, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('getReportIndex', since, until);
        serverTiming.recordServerCacheTelemetry('getReportIndex', until - since, cacheHit, cacheSize);
      },
      fetch: async (repoId, stale, signal) => {
        if (stale) {
          const metadata = await this._s3objects.reportIndex.getMetadata(repoId, signal);
          if (metadata?.etag === stale.etag())
            return stale;
        }
        const r = await this._s3objects.reportIndex.get(repoId, signal);
        signal.throwIfAborted();
        if (!r || r.version > ReportIndex.REPORT_INDEX_VERSION)
          return undefined;
        return new ReportIndex.ReportIndex(repoId.projectPublicId, r);
      },
    });

    this._xReportIndexChanged = xnotify.createChannel('report-index-cache-changed', async id => {
      this._cachedStorage.refresh(id);
    });
  }

  setStripeApp(stripeApp?: StripeApp) {
    this._stripeApp = stripeApp;
  }

  async initialize() {
    await this._cron.setRecurringJob('enforce-data-retention', ms('1 day') as FlakinessReport.DurationMS, async () => {
      const projects = await this._db.projects.all();
      await this._reportIndexQueue.sendMany(projects.map(project => ({
        data: {
          type: 'enforce-data-retention',
          projectPublicId: project.project_public_id,
        },
        options: {
          // First, we don't want to have multiple jobs with the same jobId. So have only one.
          jobId: `drop-old-reports-${project.project_public_id}`,
          // Next, we don't want to conflict with other jobs that might be adding reports to the
          // index.
          concurrencyId: project.project_public_id,
        }
      })));
    });

    // As we start, we schedule a job that makes sure that we enlisted all the reports.
    // In case of a version change, this will re-build index.
    const projects = await this._db.projects.all();
    await this._reportIndexQueue.sendMany(projects.map(project => ({
      data: {
        type: 'ensure-all-reports-are-in-index',
        projectPublicId: project.project_public_id,
      },
      options: {
        // First, we don't want to have multiple jobs with the same jobId. So have only one.
        jobId: `ensure-all-reports-are-in-index-${project.project_public_id}`,
        // Next, we don't want to conflict with other jobs that might be adding reports to the
        // index.
        concurrencyId: project.project_public_id,
      }
    })));
  }

  async projectMetrics(projectPublicId: ProjectPublicId): Promise<WireTypes.DailyMetrics[]> {
    const index = await this._cachedStorage.get({ projectPublicId: projectPublicId });
    return index?.metrics() ?? [];
  }

  async orgMetrics(orgId: OrgId) {
    const projectPublicIds = await this._db.orgs.getProjectPublicIds(orgId);
    const tsToMetrics = new Multimap<FlakinessReport.UnixTimestampMS, WireTypes.DailyMetrics>();
    for (const projectPublicId of projectPublicIds) {
      const index = await this._cachedStorage.get({ projectPublicId: projectPublicId });
      if (!index)
        continue;
      for (const metric of index.metrics())
        tsToMetrics.set(metric.dayTimestampMs, metric);
    }

    const result: WireTypes.DailyMetrics[] = [];
    for (const [dayMs, metrics] of tsToMetrics)
      result.push(aggregateDailyMetrics(dayMs, metrics));
    result.sort((a, b) => a.dayTimestampMs - b.dayTimestampMs);
    return result;
  }

  async reportIndex(projectPublicId: ProjectPublicId): Promise<ReportIndex.ReportIndex> {
    return await this._cachedStorage.get({ projectPublicId: projectPublicId }) ?? new ReportIndex.ReportIndex(projectPublicId, undefined);
  }

  async start(workerName: string) {
    assert(!this._reportIndexWorker);
    this._reportIndexWorker = this._reportIndexQueue.createWorker(workerName, async (job, options) => {
      if (job.data.type === 'add-reports')
        await this._addReportsToIndex(job.data.projectPublicId, job.data.reportIds, options);
      else if (job.data.type === 'enforce-data-retention')
        await this._enforceDataRetention(job.data.projectPublicId, options);
      else if (job.data.type === 'ensure-all-reports-are-in-index')
        await this._ensureAllReportsAreInIndexAndShards(job.data.projectPublicId, options);
    });
  }

  async stop() {
    assert(this._reportIndexWorker);
    await this._reportIndexWorker.stop();
    this._reportIndexWorker = undefined;
  }

  async startUpload(project: Project, expiration: FlakinessReport.DurationMS): Promise<S3Report.Id> {
    const consequtiveReportId = await this._db.projects.incReportCount(project.project_id) as Stats.RunId;
    const reportId: S3Report.Id = {
      projectPublicId: project.project_public_id,
      reportId: consequtiveReportId,
    };
    // We will check the upload after the expiration.
    await this._scheduleAddReports(reportId.projectPublicId, Ranges.from(reportId.reportId), expiration, S3Report.path(reportId));
    return reportId;
  }

  async presignedReportUrl(reportId: S3Report.Id, expiration: FlakinessReport.DurationMS) {
    return await this._s3objects.reports.createReportUploadURLs({
      reportId,
      expiration,
    });
  }

  async presignedAttachmentUrl(reportId: S3Report.Id, attachmentId: string, expiration: FlakinessReport.DurationMS) {
    return await this._s3objects.reports.createAttachmentUploadURL({
      reportId,
      attachmentId,
      expiration,
    });
  }

  private async _scheduleAddReports(projectPublicId: ProjectPublicId, reportIds: Ranges.Ranges<Stats.RunId>, expiration: FlakinessReport.DurationMS, jobId?: string) {
    if (!reportIds.length)
      return;
    await this._reportIndexQueue.send({
      type: 'add-reports',
      projectPublicId,
      reportIds,
    }, {
      jobId,
      // We wait for "expiration" for report to be uploaded.
      delayMs: expiration,
      // Since we change sharding, we have to limit this to per-project.
      concurrencyId: projectPublicId,
    })
  }

  async completeUpload(reportId: S3Report.Id) {
    await this._reportIndexQueue.setJobDelay(S3Report.path(reportId), 0);
  }

  private async _ensureAllReportsAreInIndexAndShards(projectPublicId: ProjectPublicId, { signal }: QueueWorkerCallbackOptions) {
    // 1. Read index file.
    const indexJSON = await this._s3objects.reportIndex.get({ projectPublicId }, signal);
    // If the index version is larger than what we can work with, then bail out.
    if (indexJSON && indexJSON.version > ReportIndex.REPORT_INDEX_VERSION)
      return;
    const index = new ReportIndex.ReportIndex(projectPublicId, indexJSON);

    // 2. Enumerate all reports and make sure they all exist in the index.
    //    This might be relatively slow, since we have to enumerate all reports. However,
    //    for 1000000 reports (2500+ reports in a day) this still should easily fit in 
    //    15 minutes: 1000000 / 1000 (entries per request) = 1000 requests ~ 15 minutes.
    for await (const reportIds of this._s3objects.reports.list(projectPublicId, signal)) {
      const reportsToAdd = reportIds.filter(reportId => !index.hasRun(reportId.reportId));
      await this._scheduleAddReports(projectPublicId, Ranges.fromList(reportsToAdd.map(report => report.reportId)), 0 as FlakinessReport.DurationMS);
    }

    // 3. Finally, let's schedule rebuild for all shards of the project.
    //    NOTE that this will also delete shards that do not belong to the index.
    for await (const shardIds of this._s3objects.stats.list(projectPublicId, signal))
      await this._statsWorker.scheduleBuild(shardIds);
  }

  private _dataRetentionDays(project: Project, billing?: WireTypes.BillingStatus): number {
    const preferredDataRetentionDays = project.preferred_data_retention_days ?? Infinity;
    const maxDataRetentionDays = billing?.subscription?.maxDataRetentionDays ?? Infinity;
    // Actual data retention is the minimum between preferred data retention and the maximum
    // allowed by the subscription.
    return Math.min(preferredDataRetentionDays, maxDataRetentionDays);
  }

  private async _enforceDataRetention(projectPublicId: ProjectPublicId, { signal }: QueueWorkerCallbackOptions) {
    const project = await this._db.projects.getByPublicId(projectPublicId);
    const org = project ? await this._db.orgs.get(project.org_id) : undefined;
    const billing = org ? await this._stripeApp?.status(org) : undefined;
    if (!project || !org)
      return;

    const dataRetentionDays = this._dataRetentionDays(project, billing);
    if (Object.is(dataRetentionDays, Infinity))
      return;

    // If report index version mismatches what we expect, then bail out.
    const indexJSON = await this._s3objects.reportIndex.get({ projectPublicId }, signal);
    if (indexJSON && indexJSON.version !== ReportIndex.REPORT_INDEX_VERSION)
      return;
    const index = new ReportIndex.ReportIndex(projectPublicId, indexJSON);

    // To enforce data retention, we should drop all the reports before the cut-off date.
    const { reportsToDelete, shardsToRebuild } = index.enforceDataRetention(dataRetentionDays);
    if (!reportsToDelete.length && !shardsToRebuild.length)
      return;

    await this._s3gc.removeReports(project.project_public_id, reportsToDelete);
    await this._s3objects.reportIndex.set({ projectPublicId }, index.etag(), index.serialize());
    await this._statsWorker.scheduleBuild(shardsToRebuild);
    await this._xReportIndexChanged.notify({ projectPublicId });
  }

  private async _addReportsToIndex(projectPublicId: ProjectPublicId, reportIds: Ranges.Ranges<Stats.RunId>, { signal }: QueueWorkerCallbackOptions) {
    const indexJSON = await this._s3objects.reportIndex.get({ projectPublicId }, signal);
    // If indexJSON version is larger than what we can product, then bail out.
    if (indexJSON && indexJSON.version > ReportIndex.REPORT_INDEX_VERSION)
      return;
    const index = new ReportIndex.ReportIndex(projectPublicId, indexJSON);

    const project = await this._db.projects.getByPublicId(projectPublicId);
    if (!project) {
      await this._s3gc.removeReports(projectPublicId, reportIds);
      return;
    }

    const shardIdsToRebuild = new Set<S3Stats.ShardId>();
    // Use 1 minute chunks to add reports to shards; split job afterwards.
    const deadlineMs = Date.now() + ms('1 minutes');
    let maxUploadTimestampMs = (project.last_upload_timestamp_seconds ?? 0) * 1000;
    while (reportIds.length && Date.now() < deadlineMs) {
      const reportId: S3Report.Id = {
        projectPublicId,
        reportId: Ranges.popInplace(reportIds)!,
      };
      if (index.hasRun(reportId.reportId))
        continue;

      const data = await this._s3objects.reports.get(reportId, signal);
      if (!data) {
        await this._s3objects.reports.remove(reportId, signal);
        continue;
      }
      const { report, lastModified } = data;
      const s3time = lastModified ? +lastModified as FlakinessReport.UnixTimestampMS : undefined;
      const bytes = await this._s3objects.reports.computeByteSize(reportId, signal);

      const validationError = FlakinessReport.validate(report);
      if (validationError) {
        const uploadTimestampMs = s3time ?? Date.now() as FlakinessReport.UnixTimestampMS;
        index.addFaultyReport({
          reportId: reportId.reportId,
          attachmentBytes: bytes.attachmentsBytes,
          reportBytes: bytes.reportBytes,
          error: validationError,
          uploadTimestampMs,
        });
        maxUploadTimestampMs = Math.max(uploadTimestampMs, maxUploadTimestampMs);
        continue;
      }
  
      const reportTime = (Math.min(report.startTimestamp + report.duration, Date.now())) as FlakinessReport.UnixTimestampMS;
      const uploadTimestampMs = s3time && !project.report_time_is_upload_time ? s3time : reportTime;
      maxUploadTimestampMs = Math.max(uploadTimestampMs, maxUploadTimestampMs);
  
      let shard = index.getShard(report.commitId);
      if (!shard) {
        shard = index.getLastShard();
        // Check shard reports size and max size. Create a new shard if either is larger.
        if (!shard || index.shardS3Reports(shard).length > MAX_REPORTS_PER_SHARD || (await this._s3objects.stats.getSizeInBytes(shard)) > MAX_SHARD_SIZE)
          shard = index.createShard();
      }

      index.addReport({
        shard,
        reportId: reportId.reportId,
        report,
        uploadTimestampMs,
        reportBytes: bytes.reportBytes,
        attachmentBytes: bytes.attachmentsBytes,
      });
      shardIdsToRebuild.add(shard.shardId);
    }
    // Change last upload time, if it updated.
    await this._db.projects.update(project.project_id, {
      last_upload_timestamp_seconds: Math.floor(maxUploadTimestampMs / 1000),
    });
    await this._s3objects.reportIndex.set({ projectPublicId }, index.etag(), index.serialize());
    await this._xReportIndexChanged.notify({ projectPublicId });
    await this._statsWorker.scheduleBuild([...shardIdsToRebuild].map(shardId => ({ projectPublicId, shardId })));
    // If this report is coming from a new commit, than we should fetch repository to see if there is actually
    // a new commit.
    await this._gitWorker.scheduleBuild({ projectPublicId, });
    if (reportIds.length)
      await this._scheduleAddReports(projectPublicId, reportIds, 0 as FlakinessReport.DurationMS);
  }
}

function aggregateDailyMetrics(timestamp: FlakinessReport.UnixTimestampMS, metrics: Iterable<WireTypes.DailyMetrics>): WireTypes.DailyMetrics {
  const agg: WireTypes.DailyMetrics = {
    dayTimestampMs: timestamp,
    runsCount: 0,
    testsCount: 0,
    totalBytes: 0,
  };
  for (const m of metrics) {
    agg.runsCount += m.runsCount;
    agg.testsCount += m.testsCount;
    agg.totalBytes += m.totalBytes;
  }
  return agg;
}
