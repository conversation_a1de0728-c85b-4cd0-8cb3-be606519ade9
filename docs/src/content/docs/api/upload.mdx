---
title: Upload API
sidebar:
  order: 1
---

import { Steps, TabItem, Tabs } from '@astrojs/starlight/components';

The Upload API utilizes [presigned URLs](https://docs.aws.amazon.com/AmazonS3/latest/userguide/ShareObjectPreSignedURL.html) to securely upload test reports and attachments to Flakiness.io. Clients request presigned URLs from the service, upload data directly to storage, and then notify Flakiness.io when the upload is complete.

The upload process consists of three sequential steps:

1. **Start Upload** - Initialize an upload session and receive an `uploadToken` to control the report upload
2. **Upload Report & Attachments** - Upload the compressed report and any test artifacts using presigned URLs
3. **Finish Upload** - Complete the upload process and mark the report for processing

## Quick Start Example

Consider uploading a test report with a single attachment to Flakiness.io:

```text
flakiness-report/
├── report.json
└── attachments/
    └── 5d41402
```

<Steps>

1. **Start Upload Session**

   Initialize the upload process to receive your upload token and presigned URLs:

   ```bash
   curl -X POST "https://flakiness.io/api/upload/start" \
     -H "Authorization: Bearer $FLAKINESS_ACCESS_TOKEN" \
     -H "Content-Type: application/json"
   ```

   Response:
   ```json
   {
     "uploadToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
     "webUrl": "/f/ff/run/9",
     "presignedReportUrl": "https://s3.flakiness.test/flakiness-data/reports/..."
   }
   ```

   The `webUrl` provides a path to track upload and processing status on the Flakiness.io platform.

2. **Compress and Upload Report**

   Reports must be compressed with Brotli for optimal efficiency:

   ```bash
   brotli --quality=9 --output=report.json.br report.json
   ```

   Upload the compressed report using the presigned URL:

   ```bash
   curl -X PUT "$REPORT_PRESIGNED_URL" \
     -H "Content-Type: application/json" \
     -H "Content-Encoding: br" \
     --data-binary @report.json.br
   ```

   :::note[Report Compression]
   Please note that report **must be** compressed with brotli. Report presigned url expects `Content-Encoding: br` header to be present.
   :::


3. **Request Attachment URLs**

   If your report includes attachments, request presigned URLs for each attachment:

   ```bash
   curl -X POST "https://flakiness.io/api/upload/attachments" \
     -H "Authorization: Bearer $UPLOAD_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"attachmentIds":["5d41402"]}'
   ```

   Response:
   ```json
   [{
     "attachmentId": "5d41402",
     "presignedUrl": "https://s3.flakiness.test/flakiness-data/reports/..."
   }]
   ```

4. **Upload Attachments**

   Upload each attachment using its corresponding presigned URL:

   ```bash
   curl -X PUT "$ATTACHMENT_PRESIGNED_URL" \
     --data-binary @attachments/5d41402
   ```

   :::tip[Attachments Compression]
   Attachments can be compressed using standard browser-supported formats (`deflate`, `br`, or `gzip`) to save space. Only compress attachments that benefit from compression.
   :::

5. **Finalize Upload**

   Complete the upload process to mark the report for processing:

   ```bash
   curl -X POST "https://flakiness.io/api/upload/finish" \
     -H "Authorization: Bearer $UPLOAD_TOKEN"
   ```

</Steps>

## API Methods

### Start Upload

<Tabs>
  <TabItem label="Overview">
    | Method | Endpoint | Authorization | Content-Type |
    |--------|----------|---------------|--------------|
    | `POST` | `/api/upload/start` | `Bearer {ACCESS_TOKEN}` | `application/json` |
  </TabItem>

  <TabItem label="cURL Example">
    ```bash
    curl -X POST "https://flakiness.io/api/upload/start" \
      -H "Authorization: Bearer $FLAKINESS_ACCESS_TOKEN" \
      -H "Content-Type: application/json"
    ```
  </TabItem>

  <TabItem label="JSON Response">
    ```json
    {
      "uploadToken": "eyJhbGciOiJIUz...",
      "webUrl": "/org/project/run/9",
      "presignedReportUrl": "https://s3.flakiness.test/flakiness-data/repor..."
    }
    ```
  </TabItem>
</Tabs>

Initializes a new upload session and returns an upload token along with a presigned URL for the report upload. The upload token is used to authenticate subsequent API calls within this upload session.

**Response Fields:**
- `uploadToken` - JWT token for authenticating subsequent upload operations
- `webUrl` - Relative path to track upload progress on the platform
- `presignedReportUrl` - Pre-signed S3 URL for uploading the compressed report

### Request Attachment URLs

<Tabs>
  <TabItem label="Overview">
    | Method | Endpoint | Authorization | Content-Type |
    |--------|----------|---------------|--------------|
    | `POST` | `/api/upload/attachments` | `Bearer {UPLOAD_TOKEN}` | `application/json` |
  </TabItem>

  <TabItem label="cURL Example">
    ```bash
    curl -X POST "https://flakiness.io/api/upload/attachments" \
      -H "Authorization: Bearer $UPLOAD_TOKEN" \
      -H "Content-Type: application/json" \
      -d '{"attachmentIds":["5d41402","abc123"]}'
    ```
  </TabItem>

  <TabItem label="JSON Response">
    ```json
    [{
      "attachmentId": "5d41402",
      "presignedUrl": "https://s3.flakiness.test/flakiness-data/re..."
    }, {
      "attachmentId": "abc123",
      "presignedUrl": "https://s3.flakiness.test/flakiness-data/re..."
    }]
    ```
  </TabItem>
</Tabs>

Generates presigned URLs for uploading test attachments. Each attachment ID from your report will receive a corresponding presigned URL for direct upload to storage.

**Request Body:**
- `attachmentIds` - Array of attachment identifiers referenced in your test report

**Response:**
- Array of objects containing `attachmentId` and corresponding `presignedUrl`

### Finish Upload

<Tabs>
  <TabItem label="Overview">
    | Method | Endpoint | Authorization | Content-Type |
    |--------|----------|---------------|--------------|
    | `POST` | `/api/upload/finish` | `Bearer {UPLOAD_TOKEN}` | N/A |
  </TabItem>

  <TabItem label="cURL Example">
    ```bash
    curl -X POST "https://flakiness.io/api/upload/finish" \
      -H "Authorization: Bearer $UPLOAD_TOKEN"
    ```
  </TabItem>

</Tabs>

Completes the upload session and marks the report for processing. This endpoint should be called after successfully uploading the report and all attachments to their respective presigned URLs.

**Response:**
- `success` - Boolean indicating successful completion of the upload process

