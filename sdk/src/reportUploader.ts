import { FlakinessReport } from '@flakiness/flakiness-report';
import { compressTextAsync, compressTextSync } from '@flakiness/shared/node/compression.js';
import assert from 'assert';
import crypto from 'crypto';
import fs from 'fs';
import { URL } from 'url';
import { httpUtils } from './httpUtils.js';
import { retryWithBackoff } from './utils.js';

type ReportUploaderOptions = {
  flakinessEndpoint: string;
  flakinessAccessToken: string;
}

export interface ExternalAttachment {
  contentType: string;
  id: FlakinessReport.AttachmentId;
  path?: string;
  body?: Buffer;
}

function sha1File(filePath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash('sha1');
    const stream = fs.createReadStream(filePath);
    stream.on('data', (chunk) => { hash.update(chunk); });

    stream.on('end', () => {
      resolve(hash.digest('hex'));
    });

    stream.on('error', (err) => {
      reject(err);
    });
  });
}

export async function createFileAttachment(contentType: string, filePath: string): Promise<ExternalAttachment> {
  return {
    contentType,
    id: await sha1File(filePath) as FlakinessReport.AttachmentId,
    path: filePath,
  };
}

export async function createDataAttachment(contentType: string, data: Buffer): Promise<ExternalAttachment> {
  const hash = crypto.createHash('sha1');
  hash.update(data);
  const id = hash.digest('hex') as FlakinessReport.AttachmentId;
  return {
    contentType,
    id,
    body: data,
  };
}

export class ReportUploader {
  static optionsFromEnv(overrides?: Partial<ReportUploaderOptions>): ReportUploaderOptions|undefined {
    const flakinessAccessToken = overrides?.flakinessAccessToken ?? process.env['FLAKINESS_ACCESS_TOKEN'];
    if (!flakinessAccessToken)
      return undefined;
    const flakinessEndpoint = overrides?.flakinessEndpoint ?? process.env['FLAKINESS_ENDPOINT'] ?? 'https://flakiness.io';
    return { flakinessAccessToken, flakinessEndpoint };
  }

  static async upload(options: Partial<ReportUploaderOptions> & {
    report: FlakinessReport.Report,
    attachments: ExternalAttachment[],
    log?: (message: string) => void,
  }): Promise<{ errorMessage?: string } | undefined> {
    const uploaderOptions = ReportUploader.optionsFromEnv(options);
    if (!uploaderOptions) {
      if (process.env.CI)
        options.log?.(`[flakiness.io] Uploading skipped since no FLAKINESS_ACCESS_TOKEN is specified`);
      return undefined; 
    }
    const uploader = new ReportUploader(uploaderOptions);
    const upload = uploader.createUpload(options.report, options.attachments);
    const uploadResult = await upload.upload();
    if (!uploadResult.success) {
      options.log?.(`[flakiness.io] X Failed to upload to ${uploaderOptions.flakinessEndpoint}: ${uploadResult.message}`);
      return { errorMessage: uploadResult.message };
    }

    options.log?.(`[flakiness.io] ✓ Report uploaded ${uploadResult.message ?? ''}`);
    if (uploadResult.reportUrl)
      options.log?.(`[flakiness.io] ${uploadResult.reportUrl}`);
  }

  private _options: ReportUploaderOptions;
  constructor(options: ReportUploaderOptions) {
    this._options = options;
  }

  createUpload(report: FlakinessReport.Report, attachments: ExternalAttachment[]) {
    const upload = new ReportUpload(this._options, report, attachments);
    return upload;
  }
}

const HTTP_BACKOFF = [100, 500, 1000, 1000, 1000, 1000];

type UploadOptions = {
  syncCompression?: boolean;
}

class ReportUpload {
  private _report: FlakinessReport.Report;
  private _attachments: ExternalAttachment[];
  private _options: ReportUploaderOptions;

  constructor(options: ReportUploaderOptions, report: FlakinessReport.Report, attachments: ExternalAttachment[]) {
    this._options = options;
    this._report = report;
    this._attachments = attachments;
  }

  private async _api<OUTPUT>(pathname: string, token: string, body?: any): Promise<{ result?: OUTPUT, error?: string }> {
    const url = new URL(this._options.flakinessEndpoint);
    url.pathname = pathname;
    return await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: body ? JSON.stringify(body) : undefined,
    }).then(async response => !response.ok ? {
      result: undefined,
      error: response.status + ' ' + url.href + ' ' + await response.text(),
    } : {
      result: await response.json() as OUTPUT,
      error: undefined,
    }).catch(error => ({
      result: undefined,
      error,
    }));
  }

  async upload(options?: UploadOptions): Promise<{ success: boolean, message?: string, reportUrl?: string }> {
    const response = await this._api<{ uploadToken: string, presignedReportUrl: string, webUrl: string, }>('/api/upload/start', this._options.flakinessAccessToken);
    if (response?.error || !response.result)
      return { success: false, message: response.error};
    const webUrl = new URL(response.result.webUrl, this._options.flakinessEndpoint).toString();

    const attachmentsPresignedUrls = await this._api<{ attachmentId: string, presignedUrl: string }[]>('/api/upload/attachments', response.result.uploadToken, {
      attachmentIds: this._attachments.map(a => a.id),
    });
    if (attachmentsPresignedUrls?.error || !attachmentsPresignedUrls.result)
      return { success: false, message: attachmentsPresignedUrls.error};

    const attachments = new Map(attachmentsPresignedUrls.result.map(a => [a.attachmentId, a.presignedUrl]));
    await Promise.all([
      this._uploadReport(JSON.stringify(this._report), response.result.presignedReportUrl, options?.syncCompression ?? false),
      ...this._attachments.map(attachment => {
        const uploadURL = attachments.get(attachment.id);
        if (!uploadURL)
          throw new Error('Internal error: missing upload URL for attachment!');
        return this._uploadAttachment(attachment, uploadURL, options?.syncCompression ?? false);
      }),
    ]);
    await this._api<{ webUrl: string }>('/api/upload/finish', response.result.uploadToken);
    return { success: true, reportUrl: webUrl };
  }

  private async _uploadReport(data: string, uploadUrl: string, syncCompression: boolean) {
    const compressed = syncCompression ? compressTextSync(data) : await compressTextAsync(data);
    const headers = {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(compressed) + '',
      'Content-Encoding': 'br',
    };
    await retryWithBackoff(async () => {
      const { request, responseDataPromise } = httpUtils.createRequest({
        url: uploadUrl,
        headers,
        method: 'put'
      });
      request.write(compressed);
      request.end();
      await responseDataPromise;
    }, HTTP_BACKOFF);
  }

  private async _uploadAttachment(attachment: ExternalAttachment, uploadUrl: string, syncCompression: boolean) {
    const mimeType = attachment.contentType.toLocaleLowerCase().trim();
    const compressable = mimeType.startsWith('text/')
      || mimeType.endsWith('+json')
      || mimeType.endsWith('+text')
      || mimeType.endsWith('+xml')
    ;
    // Stream file only if there's attachment path and we should NOT compress it.
    if (!compressable && attachment.path) {
      const attachmentPath = attachment.path;
      await retryWithBackoff(async () => {
        const { request, responseDataPromise } = httpUtils.createRequest({
          url: uploadUrl,
          headers: {
            'Content-Type': attachment.contentType,
            'Content-Length': (await fs.promises.stat(attachmentPath)).size + '',
          },
          method: 'put'
        });
        fs.createReadStream(attachmentPath)
          .pipe(request);
        await responseDataPromise;
      }, HTTP_BACKOFF);
      return;
    }
    let buffer = attachment.body ? attachment.body : 
      attachment.path ? await fs.promises.readFile(attachment.path) :
      undefined;
    assert(buffer);

    const encoding = compressable ? 'br' : undefined;

    if (compressable)
      buffer = syncCompression ? compressTextSync(buffer) : await compressTextAsync(buffer);

    const headers = {
      'Content-Type': attachment.contentType,
      'Content-Length': Buffer.byteLength(buffer) + '',
      'Content-Encoding': encoding,
    };

    await retryWithBackoff(async () => {
      const { request, responseDataPromise } = httpUtils.createRequest({
        url: uploadUrl,
        headers,
        method: 'put'
      });
      request.write(buffer);
      request.end();
      await responseDataPromise;
    }, HTTP_BACKOFF);
  }
}